将 data/feature.csv 中的 9+7=16 维特征作为常见的分类模型的输入，以 label 作为标签，使用 svm、xgboost 等模型进行训练，展示分类效果

## 项目结构
```
baseline/
├── svm_classify.py             # svm 模型
├── catboost_classify.py        # catboost 模型
├── readme.md                   # 关于baseline的说明文档
└── models/                     # 模型保存目录，非必要
data/
├── data_0825.csv                    # 输入数据文件
```

## 数据

/home/<USER>/PyProject/mmf/mmf/data/data_0825.csv

columns: 'userId', 'content', 'cost', 'days', 'follower', 'following', 'ifPic',
       'like', 'peopleNum', 'picNum', 'province', 'spots', 'title', 'travelId',
       'uploadTime', 'source', 'like_norm', 'label', 'nima', 'hyperiqa',
       'contrast', 'selfsimilarity', 'simplicity', 'symmetry', 'score_abs',
       'score_con', 'score_coh','image_path'

- 字段描述：userId：用户id；content：原始游记正文；follower：粉丝数；following：关注数；like：点赞数；like_norm：消除时间因素并log化的点赞数；spots：景点集合，形如："黄山|天柱山|泰山"；title：游记标题；travelId：游记id；uploadTime：游记发表时间（形如：2021/07/08）；label：游记是否流行，1表示流行，0表示不流行；source是经过清理后的游记正文，相较于data.csv文件中的content，去除无关、无用字符，用于提取文本语义特征；image_path为travelId对应的图片路径，list类型，其中每个元素为一个路径。
- 其他不太重要的字段：cost：出行费用；days：出行时间；ifPic：是否有图片，1表示有，0表示没有；peopleNum：出行人数；picNum：游记中的图片数量；province：出行所在的省份 

输入到svm、catboost中的16个特征的说明
- 已经计算得到的 9 个特征：nima、hyperiqa、contrast、selfsimilarity、simplicity、symmetry、'score_abs', 'score_con', 'score_coh'
- 需要计算的 7 个特征：'related_travel_num', 'related_user_num', 'related_travel_p', 'related_user_p', 'follower', 'following', 'count'。
- 3个特征的计算说明：'follower', 'following'可以直接从 data_0825.csv 中读取。'count'需要在训练过程中计算，计算方式是统计某个userId对应的不同的travelId的个数，可参考data_user.py
- 4个特征的计算说明：related_travel_num：对每个景点统计出现过的不同 travelId 数量（groupby spot 后 nunique(travelId)）。related_user_num：对每个景点统计涉及的不同 userId 数量（groupby spot 后 nunique(userId)）。related_travel_p= related_travel_num / total_travels，其中 total_travels 是筛选后数据中全部不同 travelId 的总数；结果保留两位小数（round(2)）。related_user_p= related_user_num / total_users，其中 total_users 是筛选后数据中全部不同 userId 的总数；同样 round(2)。可参考data_spot.py
- spots字段记录有多个景点，需要根据|字符拆分出景点，根据每个景点分别统计，然后取该travelId对应的所有景点的平均值

验证阶段，直接使用基于训练集的统计结果作为输入
测试阶段，基于训练集和验证集（除测试集以外的所有数据）来重新统计7个特征（'related_travel_num', 'related_user_num', 'related_travel_p', 'related_user_p', 'follower', 'following', 'count'）

如果在验证集或测试集中出现新的userId或景点，那么计算其他userId或景点已有数据的平均值来填充

data_0825.csv的第一行数据如下：
```
0,*********,前言说说这次旅行那座古老的城，宛如一本厚重的史书，在时光的长河中静静等待着我。西安，你的名字就像一阵古老的风，轻轻拂过我的心田，带来了千年的故事与无尽的遐想。第1天长水机场观景平台因为看了《快乐再出发》，被西安的碳水所吸引，于是趁着端午，老凌和我，当然还有个小尾巴，于是来到了西安。西安是一个充满历史韵味和美食诱惑的城市，一次旅行就能带给你丰富的体验。安仁坊遗址展示馆大雁塔大唐不夜城第2天秦始皇帝陵博物院(兵马俑)骊山西安千古情第3天洒金桥女寺陕西历史博物馆西安钟楼西安，你宛如一部古老而厚重的史书，每一页都写满了故事。那古老的城墙，像一位沉默的守护者，见证了无数的岁月变迁。我曾漫步在你的大街小巷，感受着那从历史深处吹来的风。此刻，我即将告别。就像一片落叶要离开滋养它的大树，心中满是不舍。那些兵马俑的雄伟身姿仿佛还在眼前，大雁塔的风铃似乎还在耳边作响。但我知道，离别是为了下一次更好的相遇。,NaN,3,107,3,1.0,1,家庭,101,陕西,大唐不夜城|陕西历史博物馆|骊山|大雁塔|西安钟楼|秦始皇帝陵博物院,西安3日游,7890310,2025-06-10,那座古老的城，宛如一本厚重的史书，在时光的长河中静静等待着我。西安，你的名字就像一阵古老的风，轻轻拂过我的心田，带来了千年的故事与无尽的遐想。\n因为看了《快乐再出发》，被西安的碳水所吸引，于是趁着端午，老凌和我，当然还有个小尾巴，于是来到了西安。西安是一个充满历史韵味和美食诱惑的城市，一次旅行就能带给你丰富的体验。\n西安，你宛如一部古老而厚重的史书，每一页都写满了故事。那古老的城墙，像一位沉默的守护者，见证了无数的岁月变迁。我曾漫步在你的大街小巷，感受着那从历史深处吹来的风。\n此刻，我即将告别。就像一片落叶要离开滋养它的大树，心中满是不舍。那些兵马俑的雄伟身姿仿佛还在眼前，大雁塔的风铃似乎还在耳边作响。但我知道，离别是为了下一次更好的相遇。,2.0,1,5.516,0.664,0.274,-0.65,0.433,0.46,4.0,4.0,4.0,"[/home/<USER>/PyProject/mmf/mmf/data/picture/7890310/5.jpg, /home/<USER>/PyProject/mmf/mmf/data/picture/7890310/7.jpg, /home/<USER>/PyProject/mmf/mmf/data/picture/7890310/8.jpg, /home/<USER>/PyProject/mmf/mmf/data/picture/7890310/9.jpg, /home/<USER>/PyProject/mmf/mmf/data/picture/7890310/2.jpg, /home/<USER>/PyProject/mmf/mmf/data/picture/7890310/4.jpg, /home/<USER>/PyProject/mmf/mmf/data/picture/7890310/1.jpg, /home/<USER>/PyProject/mmf/mmf/data/picture/7890310/6.jpg, /home/<USER>/PyProject/mmf/mmf/data/picture/7890310/3.jpg, /home/<USER>/PyProject/mmf/mmf/data/picture/7890310/0.jpg]"
```

## 模型

### 数据集划分
训练: 测试 = 0.8: 0.2。按照uploadTime字段排序数据，以最新的20%数据作为测试集
k折时序交叉验证，默认值为5
  - 首先将数据集按照时间顺序排列，将最新的 20% 数据作为测试集，剩下的80%作为训练集
  - 对训练集做k折时序交叉验证，训练集按照时间顺序排序，然后分成k份
  - 让每一组超参数模型在k折数据上训练、验证，以k折中验证集的平均指标作为该组超参数模型的最终指标
  - 对比所有超参数模型，选择最优的一组，在整个训练集上训练数据，然后在20%测试集上做测试

注意：一些关于用户（'follower', 'following', 'count'）和景点（'related_travel_num', 'related_user_num', 'related_travel_p', 'related_user_p',）的特征需要计算，在data_0825.csv中不提供

### 输出指标
acc、f1、precision、recall

### 模型类型
#### 模型1：svm
svm对特征的尺度敏感，需要进行特征缩放，把每个特征的数据都转换成均值为0，标准差为1的分布

一般过程：

阶段一：模型选择与调优（使用交叉验证）
这个阶段的目标是找到最好的模型和超参数。测试集在这一阶段完全不参与。
假设我们对80%的训练集做5折交叉验证：
第1折：
学习：使用第2、3、4、5份数据来计算所有统计特征（count, related_travel_num等）并学习特征缩放器（Scaler），训练模型
应用：用上面学到的规则在第1份数据（验证集）做评估
第2折：
学习：使用第1、3、4、5份数据来学习规则，训练模型
应用：用新学到的规则在第2份数据（验证集）评估
...以此类推...
这个阶段结束后，我们根据5次验证的平均表现，选出了最优的超参数。
阶段二：最终模型训练与评估
这个阶段的目标是用选好的超参数，训练一个最终模型，并在真正的“未知数据”上进行一次性评估。
学习：拿出全部80%的训练集（除20%测试集以外的所有数据），在这完整的80%数据上，重新计算一次所有的统计特征，训练模型
测试： 在转换后的20%测试集上进行预测和评估，得到最终的报告分数（acc, f1等）。


#### 模型2：catboost

数据集划分：k折时序交叉验证，和svm一致
特征缩放：和svm一致
特征输入：和svm一致，16维


---

| baseline              | 实现情况 |
|-----------------------|------|
| svm+当前固定特征            |      |
| catboost+当前固定特征       |      |
| hyfea（catboost+共同的特征） |      |
| Ours                  |      |
| MMF、ustc—模型           |      |


#### 模型3：hyfea

参考链接：https://github.com/runnerxin/HyFea

需要的字段：图像；发布时间；userId；图片数；travelId；标题  TF-IDF+SVD  Glove；粉丝；关注数

模型：catboost

注意事项：
1. 最终被**拼接（concatenated）**成一个单一的、高维的特征向量，然后输入到CatBoost模型中进行训练
2. 按时间顺序将数据分为训练集和测试集，以在本地测试中获得模型性能"。这对于避免时间序列数据中的未来信息泄露至关重要。
3. 对于HyFea(K)版本，此过程在K折交叉验证的框架下进行。

| 类别         | 特征名称                                                     | 描述与提取方法                                               | 数据类型      | 维度/表示方式                                           | 处理                                                         |
| :----------- | :----------------------------------------------------------- | :----------------------------------------------------------- | :------------ | :------------------------------------------------------ | ------------------------------------------------------------ |
| **图像**     | `ImgLength`, `ImgWidth`, `Pixel`                             | 图片的长度、宽度和总像素数。                                 | 数值型        | 1                                                       | ✔️                                                            |
|              | `ImgModel`                                                   | 图片的色彩模式。                                             | 类别型        | 4种模式: P, 1, RGB, CMYK                                | ✔️                                                            |
| **类别**     | `Category`, `Subcategory`, `Concept`                         | 数据集提供的三级层级分类。                                   | 类别型        | `Category`: 11类, `Subcategory`: 77类, `Concept`: 668类 | ❌                                                            |
| **时空**     | 经纬度 (Longitude, Latitude)                                 | 帖子发布的基础地理坐标。                                     | 数值型        | 1                                                       | ❌                                                            |
|              | `HourInDay`, `HourInWeek`, `DayInWeek`, `DayInMonth`, `WeekInYear` | 基于发布时间戳构建的多个时间维度特征。                       | 类别型/数值型 | 1                                                       | `DayInWeek`, `DayInMonth`, `WeekInYear`                      |
| **用户画像** | `Uid`                                                        | 用户的唯一ID。                                               | 类别型        | 1                                                       | `Uid`，需要参考代码，如何处理                                |
|              | `UserPostCount`, `PhotoCount`                                | 用户发布过的总帖子数和总照片数。                             | 数值型        | 1                                                       | ✔️                                                            |
|              | `Ispro`                                                      | 用户是否为专业会员。                                         | 布尔型        | 1                                                       | ❌                                                            |
|              | 用户时间信息                                                 | 用户首次发布图片和首次拍摄图片的时间戳，以及这些时间与当前帖子发布时间的差值。 | 数值型        | 多个                                                    | 用户首次发布帖子（数据集中）；这些时间与当前帖子发布时间的差值 |
| **标签**     | 媒体文件类型                                                 | 'photo' 或 'video'。                                         | 类别型        | 1                                                       | ❌                                                            |
|              | `TitleLen`, `TitleNumber`, `AlltagsLen`, `AlltagsNumber`     | 标题和标签的单词数与字符数。                                 | 数值型        | 1                                                       | `TitleLen`                                                   |
|              | **TF-IDF + SVD**                                             | 对标题和标签文本计算词频-逆文档频率（TF-IDF）特征，然后使用奇异值分解（SVD）降维至20维。 | 数值型        | 20维向量                                                | 只对标题处理                                                 |
|              | **GloVe Embedding**                                          | 使用预训练的GloVe模型，将标题和标签中的词向量取平均，以表示其语义信息。 | 数值型        | GloVe词向量维度                                         | 只对标题处理                                                 |
| **其他**     | `Ispublic`                                                   | 帖子是否为公开可见。                                         | 布尔型        | 1                                                       | ❌                                                            |
|              | `FollowingCount`, `FollowerCount`, `TotalPhoto`, `TotalGroup`, `TotalFaves`, `TotalGeotagged`, `TotalTags`, `TotalViews` | **通过爬虫从用户主页 (`pathalias`字段指示) 爬取的额外信息**，包括关注数、粉丝数、总照片数、总群组数、总收藏数、总地理标记数、总标签数和总浏览量。 | 数值型        | 1                                                       | `FollowingCount`, `FollowerCount`, `TotalPhoto`,             |
|              | `MeanFaves`, `MeanTags`, `MeanView`                          | 基于用户所有图片计算的平均值特征，如平均收藏数、平均标签数和平均浏览量。 | 数值型        | 1                                                       | 如果引入点赞、浏览相关数据影响较大                           |

