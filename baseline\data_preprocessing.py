"""
数据预处理模块
负责数据加载、特征计算和数据清洗功能
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')


class DataPreprocessor:
    """
    数据预处理器类
    
    主要功能：
    1. 加载原始数据
    2. 计算16维特征（9个已有特征 + 7个需要计算的特征）
    3. 数据清洗和格式化
    4. 按时间顺序划分数据集
    """
    
    def __init__(self, data_path: str = 'data/data_0825.csv'):
        """
        初始化数据预处理器
        
        参数:
            data_path (str): 数据文件路径
        """
        self.data_path = data_path
        self.data = None
        self.feature_columns = None
        
        # 定义16维特征列名
        # 已有的9个特征
        self.existing_features = [
            'nima', 'hyperiqa', 'contrast', 'selfsimilarity', 
            'simplicity', 'symmetry', 'score_abs', 'score_con', 'score_coh'
        ]
        
        # 需要计算的7个特征
        self.computed_features = [
            'related_travel_num', 'related_user_num', 'related_travel_p', 
            'related_user_p', 'follower', 'following', 'count'
        ]
        
        # 所有16维特征
        self.all_features = self.existing_features + self.computed_features
    
    def load_data(self) -> pd.DataFrame:
        """
        加载原始数据
        
        返回:
            pd.DataFrame: 加载的数据
        """
        try:
            self.data = pd.read_csv(self.data_path)
            print(f"成功加载数据，共 {len(self.data)} 条记录")
            print(f"数据列名: {list(self.data.columns)}")
            return self.data
        except FileNotFoundError:
            print(f"数据文件 {self.data_path} 不存在")
            # 创建示例数据用于测试
            return self._create_sample_data()
        except Exception as e:
            print(f"加载数据时出错: {e}")
            return self._create_sample_data()
    
    def _create_sample_data(self) -> pd.DataFrame:
        """
        创建示例数据用于测试
        
        返回:
            pd.DataFrame: 示例数据
        """
        print("创建示例数据用于测试...")
        np.random.seed(42)
        n_samples = 1000
        
        # 创建示例数据
        sample_data = {
            'userId': np.random.randint(100000, 999999, n_samples),
            'travelId': np.random.randint(1000000, 9999999, n_samples),
            'uploadTime': pd.date_range('2020-01-01', '2025-08-01', periods=n_samples),
            'follower': np.random.randint(0, 10000, n_samples),
            'following': np.random.randint(0, 5000, n_samples),
            'spots': [f"景点{i%10}|景点{(i+1)%10}" for i in range(n_samples)],
            'label': np.random.randint(0, 2, n_samples),
            # 已有的9个特征
            'nima': np.random.normal(5, 1, n_samples),
            'hyperiqa': np.random.normal(0.5, 0.2, n_samples),
            'contrast': np.random.normal(0, 0.5, n_samples),
            'selfsimilarity': np.random.normal(0, 0.3, n_samples),
            'simplicity': np.random.normal(0.5, 0.2, n_samples),
            'symmetry': np.random.normal(0.5, 0.2, n_samples),
            'score_abs': np.random.normal(4, 1, n_samples),
            'score_con': np.random.normal(4, 1, n_samples),
            'score_coh': np.random.normal(4, 1, n_samples),
        }
        
        self.data = pd.DataFrame(sample_data)
        print(f"创建了 {len(self.data)} 条示例数据")
        return self.data
    
    def parse_spots(self, spots_str: str) -> List[str]:
        """
        解析景点字符串，按|分割
        
        参数:
            spots_str (str): 景点字符串，如"黄山|天柱山|泰山"
            
        返回:
            List[str]: 景点列表
        """
        if pd.isna(spots_str) or spots_str == '':
            return []
        return [spot.strip() for spot in str(spots_str).split('|') if spot.strip()]
    
    def compute_user_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算用户相关特征
        
        参数:
            data (pd.DataFrame): 输入数据
            
        返回:
            pd.DataFrame: 包含用户特征的数据
        """
        # 计算count特征：统计每个userId对应的不同travelId数量
        user_travel_count = data.groupby('userId')['travelId'].nunique().reset_index()
        user_travel_count.columns = ['userId', 'count']
        
        # 合并count特征
        data = data.merge(user_travel_count, on='userId', how='left')
        
        # follower和following直接从原数据获取（如果存在）
        if 'follower' not in data.columns:
            data['follower'] = 0
        if 'following' not in data.columns:
            data['following'] = 0
            
        return data

    def compute_spot_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算景点相关特征

        参数:
            data (pd.DataFrame): 输入数据

        返回:
            pd.DataFrame: 包含景点特征的数据
        """
        # 展开景点数据，每个景点一行
        spot_records = []
        for idx, row in data.iterrows():
            spots = self.parse_spots(row['spots'])
            for spot in spots:
                spot_records.append({
                    'travelId': row['travelId'],
                    'userId': row['userId'],
                    'spot': spot
                })

        if not spot_records:
            # 如果没有景点数据，填充默认值
            data['related_travel_num'] = 0
            data['related_user_num'] = 0
            data['related_travel_p'] = 0.0
            data['related_user_p'] = 0.0
            return data

        spot_df = pd.DataFrame(spot_records)

        # 计算每个景点的统计信息
        spot_stats = spot_df.groupby('spot').agg({
            'travelId': 'nunique',  # related_travel_num
            'userId': 'nunique'     # related_user_num
        }).reset_index()
        spot_stats.columns = ['spot', 'related_travel_num', 'related_user_num']

        # 计算总数用于比例计算
        total_travels = data['travelId'].nunique()
        total_users = data['userId'].nunique()

        # 计算比例特征
        spot_stats['related_travel_p'] = (spot_stats['related_travel_num'] / total_travels).round(2)
        spot_stats['related_user_p'] = (spot_stats['related_user_num'] / total_users).round(2)

        # 为每个travelId计算其所有景点的平均特征值
        travel_spot_features = []
        for idx, row in data.iterrows():
            spots = self.parse_spots(row['spots'])
            if spots:
                # 获取该travelId所有景点的特征
                spot_features = spot_stats[spot_stats['spot'].isin(spots)]
                if not spot_features.empty:
                    # 计算平均值
                    avg_features = {
                        'travelId': row['travelId'],
                        'related_travel_num': spot_features['related_travel_num'].mean(),
                        'related_user_num': spot_features['related_user_num'].mean(),
                        'related_travel_p': spot_features['related_travel_p'].mean(),
                        'related_user_p': spot_features['related_user_p'].mean()
                    }
                else:
                    # 如果景点不在统计中，使用平均值填充
                    avg_features = {
                        'travelId': row['travelId'],
                        'related_travel_num': spot_stats['related_travel_num'].mean(),
                        'related_user_num': spot_stats['related_user_num'].mean(),
                        'related_travel_p': spot_stats['related_travel_p'].mean(),
                        'related_user_p': spot_stats['related_user_p'].mean()
                    }
            else:
                # 如果没有景点，使用平均值填充
                avg_features = {
                    'travelId': row['travelId'],
                    'related_travel_num': spot_stats['related_travel_num'].mean() if not spot_stats.empty else 0,
                    'related_user_num': spot_stats['related_user_num'].mean() if not spot_stats.empty else 0,
                    'related_travel_p': spot_stats['related_travel_p'].mean() if not spot_stats.empty else 0.0,
                    'related_user_p': spot_stats['related_user_p'].mean() if not spot_stats.empty else 0.0
                }
            travel_spot_features.append(avg_features)

        # 转换为DataFrame并合并
        travel_features_df = pd.DataFrame(travel_spot_features)
        data = data.merge(travel_features_df, on='travelId', how='left')

        return data

    def compute_all_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算所有16维特征

        参数:
            data (pd.DataFrame): 输入数据

        返回:
            pd.DataFrame: 包含所有特征的数据
        """
        print("开始计算特征...")

        # 计算用户相关特征
        data = self.compute_user_features(data)
        print("用户特征计算完成")

        # 计算景点相关特征
        data = self.compute_spot_features(data)
        print("景点特征计算完成")

        # 检查所有特征是否存在
        missing_features = [f for f in self.all_features if f not in data.columns]
        if missing_features:
            print(f"缺失特征: {missing_features}")
            # 用0填充缺失特征
            for feature in missing_features:
                data[feature] = 0

        print(f"特征计算完成，共 {len(self.all_features)} 个特征")
        return data

    def clean_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        数据清洗

        参数:
            data (pd.DataFrame): 输入数据

        返回:
            pd.DataFrame: 清洗后的数据
        """
        print("开始数据清洗...")
        original_len = len(data)

        # 删除标签缺失的记录
        data = data.dropna(subset=['label'])

        # 确保uploadTime是datetime类型
        if 'uploadTime' in data.columns:
            data['uploadTime'] = pd.to_datetime(data['uploadTime'], errors='coerce')
            # 删除时间解析失败的记录
            data = data.dropna(subset=['uploadTime'])

        # 填充特征中的缺失值
        for feature in self.all_features:
            if feature in data.columns:
                if data[feature].dtype in ['float64', 'int64']:
                    # 数值特征用均值填充
                    data[feature] = data[feature].fillna(data[feature].mean())
                else:
                    # 其他类型用0填充
                    data[feature] = data[feature].fillna(0)

        # 删除重复记录
        data = data.drop_duplicates(subset=['travelId'])

        print(f"数据清洗完成，从 {original_len} 条记录减少到 {len(data)} 条记录")
        return data

    def split_by_time(self, data: pd.DataFrame, test_ratio: float = 0.2) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        按时间顺序划分训练集和测试集

        参数:
            data (pd.DataFrame): 输入数据
            test_ratio (float): 测试集比例，默认0.2

        返回:
            Tuple[pd.DataFrame, pd.DataFrame]: (训练集, 测试集)
        """
        print(f"按时间顺序划分数据集，测试集比例: {test_ratio}")

        # 按上传时间排序
        if 'uploadTime' in data.columns:
            data = data.sort_values('uploadTime').reset_index(drop=True)
        else:
            print("警告: 没有uploadTime列，使用随机划分")
            data = data.sample(frac=1, random_state=42).reset_index(drop=True)

        # 计算分割点
        split_idx = int(len(data) * (1 - test_ratio))

        train_data = data.iloc[:split_idx].copy()
        test_data = data.iloc[split_idx:].copy()

        print(f"训练集: {len(train_data)} 条记录")
        print(f"测试集: {len(test_data)} 条记录")

        return train_data, test_data

    def get_feature_matrix(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        获取特征矩阵和标签向量

        参数:
            data (pd.DataFrame): 输入数据

        返回:
            Tuple[np.ndarray, np.ndarray]: (特征矩阵X, 标签向量y)
        """
        # 提取16维特征
        X = data[self.all_features].values
        y = data['label'].values

        print(f"特征矩阵形状: {X.shape}")
        print(f"标签向量形状: {y.shape}")
        print(f"正样本比例: {np.mean(y):.3f}")

        return X, y

    def process_data(self, compute_features: bool = True) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """
        完整的数据处理流程

        参数:
            compute_features (bool): 是否重新计算特征

        返回:
            Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]: (X_train, X_test, y_train, y_test)
        """
        # 加载数据
        data = self.load_data()

        if compute_features:
            # 计算特征
            data = self.compute_all_features(data)

        # 数据清洗
        data = self.clean_data(data)

        # 按时间划分数据集
        train_data, test_data = self.split_by_time(data)

        # 获取特征矩阵和标签
        X_train, y_train = self.get_feature_matrix(train_data)
        X_test, y_test = self.get_feature_matrix(test_data)

        return X_train, X_test, y_train, y_test
