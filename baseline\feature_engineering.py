"""
特征工程模块
负责16维特征的提取、计算和管理
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')


class FeatureEngineer:
    """
    特征工程器类
    
    主要功能：
    1. 管理16维特征的定义和计算
    2. 特征缩放和标准化
    3. 处理新数据的特征计算（验证集和测试集）
    4. 特征统计信息管理
    """
    
    def __init__(self):
        """
        初始化特征工程器
        """
        # 定义16维特征列名
        # 已有的9个特征
        self.existing_features = [
            'nima', 'hyperiqa', 'contrast', 'selfsimilarity', 
            'simplicity', 'symmetry', 'score_abs', 'score_con', 'score_coh'
        ]
        
        # 需要计算的7个特征
        self.computed_features = [
            'related_travel_num', 'related_user_num', 'related_travel_p', 
            'related_user_p', 'follower', 'following', 'count'
        ]
        
        # 所有16维特征
        self.all_features = self.existing_features + self.computed_features
        
        # 特征缩放器
        self.scaler = StandardScaler()
        self.is_fitted = False
        
        # 统计信息缓存
        self.spot_stats = None
        self.user_stats = None
        self.feature_stats = None
    
    def compute_spot_statistics(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算景点统计信息
        
        参数:
            data (pd.DataFrame): 训练数据
            
        返回:
            pd.DataFrame: 景点统计信息
        """
        print("计算景点统计信息...")
        
        # 展开景点数据
        spot_records = []
        for idx, row in data.iterrows():
            spots = self._parse_spots(row['spots'])
            for spot in spots:
                spot_records.append({
                    'travelId': row['travelId'],
                    'userId': row['userId'],
                    'spot': spot
                })
        
        if not spot_records:
            return pd.DataFrame()
        
        spot_df = pd.DataFrame(spot_records)
        
        # 计算每个景点的统计信息
        spot_stats = spot_df.groupby('spot').agg({
            'travelId': 'nunique',  # related_travel_num
            'userId': 'nunique'     # related_user_num
        }).reset_index()
        spot_stats.columns = ['spot', 'related_travel_num', 'related_user_num']
        
        # 计算总数用于比例计算
        total_travels = data['travelId'].nunique()
        total_users = data['userId'].nunique()
        
        # 计算比例特征
        spot_stats['related_travel_p'] = (spot_stats['related_travel_num'] / total_travels).round(2)
        spot_stats['related_user_p'] = (spot_stats['related_user_num'] / total_users).round(2)
        
        self.spot_stats = spot_stats
        print(f"计算了 {len(spot_stats)} 个景点的统计信息")
        return spot_stats
    
    def compute_user_statistics(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算用户统计信息
        
        参数:
            data (pd.DataFrame): 训练数据
            
        返回:
            pd.DataFrame: 用户统计信息
        """
        print("计算用户统计信息...")
        
        # 计算每个用户的travelId数量
        user_stats = data.groupby('userId').agg({
            'travelId': 'nunique',
            'follower': 'first',  # 假设同一用户的follower和following是一致的
            'following': 'first'
        }).reset_index()
        user_stats.columns = ['userId', 'count', 'follower', 'following']
        
        self.user_stats = user_stats
        print(f"计算了 {len(user_stats)} 个用户的统计信息")
        return user_stats
    
    def _parse_spots(self, spots_str: str) -> List[str]:
        """
        解析景点字符串
        
        参数:
            spots_str (str): 景点字符串
            
        返回:
            List[str]: 景点列表
        """
        if pd.isna(spots_str) or spots_str == '':
            return []
        return [spot.strip() for spot in str(spots_str).split('|') if spot.strip()]
    
    def extract_spot_features(self, data: pd.DataFrame, use_cached_stats: bool = True) -> pd.DataFrame:
        """
        为数据提取景点相关特征
        
        参数:
            data (pd.DataFrame): 输入数据
            use_cached_stats (bool): 是否使用缓存的统计信息
            
        返回:
            pd.DataFrame: 包含景点特征的数据
        """
        if self.spot_stats is None and use_cached_stats:
            raise ValueError("没有可用的景点统计信息，请先调用compute_spot_statistics")
        
        spot_stats = self.spot_stats if use_cached_stats else self.compute_spot_statistics(data)
        
        if spot_stats.empty:
            # 如果没有景点统计信息，填充默认值
            data['related_travel_num'] = 0
            data['related_user_num'] = 0
            data['related_travel_p'] = 0.0
            data['related_user_p'] = 0.0
            return data
        
        # 计算平均值用于填充未知景点
        avg_travel_num = spot_stats['related_travel_num'].mean()
        avg_user_num = spot_stats['related_user_num'].mean()
        avg_travel_p = spot_stats['related_travel_p'].mean()
        avg_user_p = spot_stats['related_user_p'].mean()
        
        # 为每个travelId计算景点特征
        travel_features = []
        for idx, row in data.iterrows():
            spots = self._parse_spots(row['spots'])
            if spots:
                # 获取该travelId所有景点的特征
                spot_features = spot_stats[spot_stats['spot'].isin(spots)]
                if not spot_features.empty:
                    # 计算平均值
                    features = {
                        'travelId': row['travelId'],
                        'related_travel_num': spot_features['related_travel_num'].mean(),
                        'related_user_num': spot_features['related_user_num'].mean(),
                        'related_travel_p': spot_features['related_travel_p'].mean(),
                        'related_user_p': spot_features['related_user_p'].mean()
                    }
                else:
                    # 未知景点，使用平均值
                    features = {
                        'travelId': row['travelId'],
                        'related_travel_num': avg_travel_num,
                        'related_user_num': avg_user_num,
                        'related_travel_p': avg_travel_p,
                        'related_user_p': avg_user_p
                    }
            else:
                # 没有景点信息，使用平均值
                features = {
                    'travelId': row['travelId'],
                    'related_travel_num': avg_travel_num,
                    'related_user_num': avg_user_num,
                    'related_travel_p': avg_travel_p,
                    'related_user_p': avg_user_p
                }
            travel_features.append(features)
        
        # 合并特征
        if travel_features:
            travel_features_df = pd.DataFrame(travel_features)
            data = data.merge(travel_features_df, on='travelId', how='left')
        else:
            # 如果没有特征数据，直接添加默认值
            data['related_travel_num'] = avg_travel_num if 'avg_travel_num' in locals() else 0
            data['related_user_num'] = avg_user_num if 'avg_user_num' in locals() else 0
            data['related_travel_p'] = avg_travel_p if 'avg_travel_p' in locals() else 0.0
            data['related_user_p'] = avg_user_p if 'avg_user_p' in locals() else 0.0

        # 确保所有景点特征都存在
        for feature in ['related_travel_num', 'related_user_num', 'related_travel_p', 'related_user_p']:
            if feature not in data.columns:
                data[feature] = 0

        return data

    def extract_user_features(self, data: pd.DataFrame, use_cached_stats: bool = True) -> pd.DataFrame:
        """
        为数据提取用户相关特征

        参数:
            data (pd.DataFrame): 输入数据
            use_cached_stats (bool): 是否使用缓存的统计信息

        返回:
            pd.DataFrame: 包含用户特征的数据
        """
        if self.user_stats is None and use_cached_stats:
            raise ValueError("没有可用的用户统计信息，请先调用compute_user_statistics")

        user_stats = self.user_stats if use_cached_stats else self.compute_user_statistics(data)

        if user_stats.empty:
            # 如果没有用户统计信息，填充默认值
            data['count'] = 1
            if 'follower' not in data.columns:
                data['follower'] = 0
            if 'following' not in data.columns:
                data['following'] = 0
            return data

        # 计算平均值用于填充未知用户
        avg_count = user_stats['count'].mean()
        avg_follower = user_stats['follower'].mean()
        avg_following = user_stats['following'].mean()

        # 合并用户特征
        data = data.merge(user_stats[['userId', 'count']], on='userId', how='left')

        # 填充缺失值
        if 'count' in data.columns:
            data['count'] = data['count'].fillna(avg_count)
        else:
            data['count'] = avg_count

        # 处理follower和following字段
        if 'follower' not in data.columns:
            # 如果原数据中没有follower和following，从统计信息中获取
            user_social = user_stats[['userId', 'follower', 'following']]
            data = data.merge(user_social, on='userId', how='left', suffixes=('', '_new'))
            if 'follower_new' in data.columns:
                data['follower'] = data['follower_new'].fillna(avg_follower)
                data = data.drop(['follower_new'], axis=1, errors='ignore')
            else:
                data['follower'] = avg_follower

            if 'following_new' in data.columns:
                data['following'] = data['following_new'].fillna(avg_following)
                data = data.drop(['following_new'], axis=1, errors='ignore')
            else:
                data['following'] = avg_following
        else:
            # 如果原数据中有follower和following，直接填充缺失值
            data['follower'] = data['follower'].fillna(avg_follower)
            if 'following' not in data.columns:
                data['following'] = avg_following
            else:
                data['following'] = data['following'].fillna(avg_following)

        return data

    def extract_all_features(self, data: pd.DataFrame, use_cached_stats: bool = True) -> pd.DataFrame:
        """
        提取所有16维特征

        参数:
            data (pd.DataFrame): 输入数据
            use_cached_stats (bool): 是否使用缓存的统计信息

        返回:
            pd.DataFrame: 包含所有特征的数据
        """
        print("提取所有特征...")

        # 如果不使用缓存统计信息，先重新计算
        if not use_cached_stats:
            self.compute_spot_statistics(data)
            self.compute_user_statistics(data)

        # 提取景点特征
        data = self.extract_spot_features(data, use_cached_stats=True)  # 使用刚计算的统计信息

        # 提取用户特征
        data = self.extract_user_features(data, use_cached_stats=True)  # 使用刚计算的统计信息

        # 确保所有特征都存在（静默填充）
        missing_features = []
        for feature in self.all_features:
            if feature not in data.columns:
                missing_features.append(feature)
                data[feature] = 0

        if missing_features:
            print(f"填充了 {len(missing_features)} 个缺失特征: {missing_features}")

        print("特征提取完成")
        return data

    def fit_scaler(self, X: np.ndarray) -> 'FeatureEngineer':
        """
        训练特征缩放器

        参数:
            X (np.ndarray): 训练集特征矩阵

        返回:
            FeatureEngineer: 自身实例
        """
        print("训练特征缩放器...")
        self.scaler.fit(X)
        self.is_fitted = True

        # 保存特征统计信息
        self.feature_stats = {
            'mean': self.scaler.mean_,
            'std': self.scaler.scale_,
            'feature_names': self.all_features
        }

        print(f"特征缩放器训练完成，特征均值: {self.scaler.mean_[:5]}...")
        print(f"特征标准差: {self.scaler.scale_[:5]}...")
        return self

    def transform_features(self, X: np.ndarray) -> np.ndarray:
        """
        应用特征缩放

        参数:
            X (np.ndarray): 特征矩阵

        返回:
            np.ndarray: 缩放后的特征矩阵
        """
        if not self.is_fitted:
            raise ValueError("特征缩放器未训练，请先调用fit_scaler")

        return self.scaler.transform(X)

    def fit_transform_features(self, X: np.ndarray) -> np.ndarray:
        """
        训练并应用特征缩放

        参数:
            X (np.ndarray): 训练集特征矩阵

        返回:
            np.ndarray: 缩放后的特征矩阵
        """
        return self.fit_scaler(X).transform_features(X)

    def get_feature_importance_names(self) -> List[str]:
        """
        获取特征名称列表

        返回:
            List[str]: 特征名称列表
        """
        return self.all_features.copy()

    def print_feature_stats(self):
        """
        打印特征统计信息
        """
        if self.feature_stats is None:
            print("特征统计信息不可用")
            return

        print("\n=== 特征统计信息 ===")
        for i, feature in enumerate(self.feature_stats['feature_names']):
            mean_val = self.feature_stats['mean'][i]
            std_val = self.feature_stats['std'][i]
            print(f"{feature:20s}: 均值={mean_val:8.4f}, 标准差={std_val:8.4f}")
        print("=" * 50)
