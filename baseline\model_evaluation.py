"""
模型评估模块
实现各种评估指标的计算和可视化
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from sklearn.metrics import (
    accuracy_score, f1_score, precision_score, recall_score, roc_auc_score,
    classification_report, confusion_matrix, roc_curve, precision_recall_curve
)
try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    PLOTTING_AVAILABLE = True
except ImportError:
    PLOTTING_AVAILABLE = False
    print("警告: matplotlib和seaborn未安装，绘图功能将不可用")

import warnings
warnings.filterwarnings('ignore')


class ModelEvaluator:
    """
    模型评估器类
    
    主要功能：
    1. 计算各种分类评估指标
    2. 生成评估报告
    3. 可视化评估结果
    4. 比较多个模型的性能
    """
    
    def __init__(self):
        """
        初始化模型评估器
        """
        self.evaluation_history = []
    
    def compute_basic_metrics(self, y_true: np.ndarray, y_pred: np.ndarray, 
                            y_pred_proba: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        计算基础评估指标
        
        参数:
            y_true (np.ndarray): 真实标签
            y_pred (np.ndarray): 预测标签
            y_pred_proba (Optional[np.ndarray]): 预测概率
            
        返回:
            Dict[str, float]: 评估指标字典
        """
        metrics = {
            'accuracy': accuracy_score(y_true, y_pred),
            'f1': f1_score(y_true, y_pred, average='binary'),
            'precision': precision_score(y_true, y_pred, average='binary', zero_division=0),
            'recall': recall_score(y_true, y_pred, average='binary', zero_division=0)
        }
        
        # 计算特异性 (Specificity)
        tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
        metrics['specificity'] = tn / (tn + fp) if (tn + fp) > 0 else 0
        
        # 计算平衡准确率 (Balanced Accuracy)
        metrics['balanced_accuracy'] = (metrics['recall'] + metrics['specificity']) / 2
        
        # 如果有预测概率，计算AUC
        if y_pred_proba is not None:
            try:
                metrics['auc'] = roc_auc_score(y_true, y_pred_proba)
            except ValueError:
                metrics['auc'] = 0.5  # 如果只有一个类别，AUC设为0.5
        
        return metrics
    
    def compute_detailed_metrics(self, y_true: np.ndarray, y_pred: np.ndarray,
                               y_pred_proba: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """
        计算详细评估指标
        
        参数:
            y_true (np.ndarray): 真实标签
            y_pred (np.ndarray): 预测标签
            y_pred_proba (Optional[np.ndarray]): 预测概率
            
        返回:
            Dict[str, Any]: 详细评估结果
        """
        # 基础指标
        basic_metrics = self.compute_basic_metrics(y_true, y_pred, y_pred_proba)
        
        # 混淆矩阵
        cm = confusion_matrix(y_true, y_pred)
        tn, fp, fn, tp = cm.ravel()
        
        # 详细指标
        detailed_metrics = {
            **basic_metrics,
            'confusion_matrix': cm,
            'true_negatives': int(tn),
            'false_positives': int(fp),
            'false_negatives': int(fn),
            'true_positives': int(tp),
            'classification_report': classification_report(y_true, y_pred, output_dict=True)
        }
        
        # 如果有预测概率，计算更多指标
        if y_pred_proba is not None:
            # ROC曲线数据
            fpr, tpr, roc_thresholds = roc_curve(y_true, y_pred_proba)
            detailed_metrics['roc_curve'] = {'fpr': fpr, 'tpr': tpr, 'thresholds': roc_thresholds}
            
            # PR曲线数据
            precision_curve, recall_curve, pr_thresholds = precision_recall_curve(y_true, y_pred_proba)
            detailed_metrics['pr_curve'] = {
                'precision': precision_curve, 
                'recall': recall_curve, 
                'thresholds': pr_thresholds
            }
            
            # 平均精确率 (Average Precision)
            from sklearn.metrics import average_precision_score
            detailed_metrics['average_precision'] = average_precision_score(y_true, y_pred_proba)
        
        return detailed_metrics
    
    def evaluate_model(self, model_name: str, y_true: np.ndarray, y_pred: np.ndarray,
                      y_pred_proba: Optional[np.ndarray] = None, 
                      additional_info: Optional[Dict] = None) -> Dict[str, Any]:
        """
        评估单个模型
        
        参数:
            model_name (str): 模型名称
            y_true (np.ndarray): 真实标签
            y_pred (np.ndarray): 预测标签
            y_pred_proba (Optional[np.ndarray]): 预测概率
            additional_info (Optional[Dict]): 额外信息
            
        返回:
            Dict[str, Any]: 评估结果
        """
        print(f"\n=== 评估模型: {model_name} ===")
        
        # 计算详细指标
        metrics = self.compute_detailed_metrics(y_true, y_pred, y_pred_proba)
        
        # 添加模型信息
        evaluation_result = {
            'model_name': model_name,
            'metrics': metrics,
            'sample_size': len(y_true),
            'positive_ratio': np.mean(y_true),
            'predicted_positive_ratio': np.mean(y_pred)
        }
        
        if additional_info:
            evaluation_result['additional_info'] = additional_info
        
        # 保存到历史记录
        self.evaluation_history.append(evaluation_result)
        
        # 打印结果
        self.print_evaluation_summary(evaluation_result)
        
        return evaluation_result
    
    def print_evaluation_summary(self, evaluation_result: Dict[str, Any]) -> None:
        """
        打印评估结果摘要
        
        参数:
            evaluation_result (Dict[str, Any]): 评估结果
        """
        metrics = evaluation_result['metrics']
        model_name = evaluation_result['model_name']
        
        print(f"模型: {model_name}")
        print(f"样本数量: {evaluation_result['sample_size']}")
        print(f"正样本比例: {evaluation_result['positive_ratio']:.3f}")
        print(f"预测正样本比例: {evaluation_result['predicted_positive_ratio']:.3f}")
        print()
        
        print("=== 主要指标 ===")
        print(f"准确率 (Accuracy):     {metrics['accuracy']:.4f}")
        print(f"F1分数 (F1-Score):     {metrics['f1']:.4f}")
        print(f"精确率 (Precision):    {metrics['precision']:.4f}")
        print(f"召回率 (Recall):       {metrics['recall']:.4f}")
        print(f"特异性 (Specificity):  {metrics['specificity']:.4f}")
        print(f"平衡准确率:            {metrics['balanced_accuracy']:.4f}")
        
        if 'auc' in metrics:
            print(f"AUC:                   {metrics['auc']:.4f}")
        if 'average_precision' in metrics:
            print(f"平均精确率:            {metrics['average_precision']:.4f}")
        
        print("\n=== 混淆矩阵 ===")
        cm = metrics['confusion_matrix']
        print(f"真负例 (TN): {metrics['true_negatives']:4d}  |  假正例 (FP): {metrics['false_positives']:4d}")
        print(f"假负例 (FN): {metrics['false_negatives']:4d}  |  真正例 (TP): {metrics['true_positives']:4d}")
        
        print("\n" + "="*50)
    
    def compare_models(self, model_results: List[Dict[str, Any]], 
                      sort_by: str = 'f1') -> pd.DataFrame:
        """
        比较多个模型的性能
        
        参数:
            model_results (List[Dict[str, Any]]): 模型评估结果列表
            sort_by (str): 排序依据的指标
            
        返回:
            pd.DataFrame: 比较结果表格
        """
        print(f"\n=== 模型性能比较 (按{sort_by}排序) ===")
        
        # 提取比较数据
        comparison_data = []
        for result in model_results:
            metrics = result['metrics']
            row = {
                '模型名称': result['model_name'],
                '准确率': metrics['accuracy'],
                'F1分数': metrics['f1'],
                '精确率': metrics['precision'],
                '召回率': metrics['recall'],
                '特异性': metrics['specificity'],
                '平衡准确率': metrics['balanced_accuracy']
            }
            
            if 'auc' in metrics:
                row['AUC'] = metrics['auc']
            if 'average_precision' in metrics:
                row['平均精确率'] = metrics['average_precision']
            
            row['样本数'] = result['sample_size']
            row['正样本比例'] = result['positive_ratio']
            
            comparison_data.append(row)
        
        # 创建DataFrame
        df = pd.DataFrame(comparison_data)
        
        # 排序
        if sort_by in ['accuracy', '准确率']:
            df = df.sort_values('准确率', ascending=False)
        elif sort_by in ['f1', 'F1分数']:
            df = df.sort_values('F1分数', ascending=False)
        elif sort_by in ['precision', '精确率']:
            df = df.sort_values('精确率', ascending=False)
        elif sort_by in ['recall', '召回率']:
            df = df.sort_values('召回率', ascending=False)
        elif sort_by in ['auc', 'AUC'] and 'AUC' in df.columns:
            df = df.sort_values('AUC', ascending=False)
        
        # 重置索引
        df = df.reset_index(drop=True)
        
        # 打印表格
        print(df.to_string(index=False, float_format='%.4f'))
        
        return df
    
    def plot_confusion_matrix(self, y_true: np.ndarray, y_pred: np.ndarray,
                            model_name: str = "Model", save_path: Optional[str] = None) -> None:
        """
        绘制混淆矩阵热图

        参数:
            y_true (np.ndarray): 真实标签
            y_pred (np.ndarray): 预测标签
            model_name (str): 模型名称
            save_path (Optional[str]): 保存路径
        """
        if not PLOTTING_AVAILABLE:
            print("绘图功能不可用，请安装matplotlib和seaborn")
            return

        cm = confusion_matrix(y_true, y_pred)

        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=['预测负例', '预测正例'],
                   yticklabels=['实际负例', '实际正例'])
        plt.title(f'{model_name} - 混淆矩阵')
        plt.ylabel('实际标签')
        plt.xlabel('预测标签')

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"混淆矩阵图已保存到: {save_path}")

        plt.show()
    
    def plot_roc_curve(self, y_true: np.ndarray, y_pred_proba: np.ndarray,
                      model_name: str = "Model", save_path: Optional[str] = None) -> None:
        """
        绘制ROC曲线

        参数:
            y_true (np.ndarray): 真实标签
            y_pred_proba (np.ndarray): 预测概率
            model_name (str): 模型名称
            save_path (Optional[str]): 保存路径
        """
        if not PLOTTING_AVAILABLE:
            print("绘图功能不可用，请安装matplotlib和seaborn")
            return

        fpr, tpr, _ = roc_curve(y_true, y_pred_proba)
        auc = roc_auc_score(y_true, y_pred_proba)

        plt.figure(figsize=(8, 6))
        plt.plot(fpr, tpr, linewidth=2, label=f'{model_name} (AUC = {auc:.3f})')
        plt.plot([0, 1], [0, 1], 'k--', linewidth=1, label='随机分类器')
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('假正例率 (False Positive Rate)')
        plt.ylabel('真正例率 (True Positive Rate)')
        plt.title(f'{model_name} - ROC曲线')
        plt.legend(loc="lower right")
        plt.grid(True, alpha=0.3)

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"ROC曲线图已保存到: {save_path}")

        plt.show()
    
    def generate_evaluation_report(self, model_results: List[Dict[str, Any]], 
                                 save_path: Optional[str] = None) -> str:
        """
        生成评估报告
        
        参数:
            model_results (List[Dict[str, Any]]): 模型评估结果列表
            save_path (Optional[str]): 报告保存路径
            
        返回:
            str: 评估报告内容
        """
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("模型评估报告")
        report_lines.append("=" * 80)
        report_lines.append("")
        
        # 总体统计
        report_lines.append("=== 总体统计 ===")
        report_lines.append(f"评估模型数量: {len(model_results)}")
        
        if model_results:
            sample_sizes = [r['sample_size'] for r in model_results]
            report_lines.append(f"平均样本数量: {np.mean(sample_sizes):.0f}")
            
            pos_ratios = [r['positive_ratio'] for r in model_results]
            report_lines.append(f"平均正样本比例: {np.mean(pos_ratios):.3f}")
        
        report_lines.append("")
        
        # 各模型详细结果
        for result in model_results:
            report_lines.append(f"=== {result['model_name']} ===")
            metrics = result['metrics']
            
            report_lines.append(f"样本数量: {result['sample_size']}")
            report_lines.append(f"正样本比例: {result['positive_ratio']:.3f}")
            report_lines.append("")
            
            report_lines.append("主要指标:")
            report_lines.append(f"  准确率:     {metrics['accuracy']:.4f}")
            report_lines.append(f"  F1分数:     {metrics['f1']:.4f}")
            report_lines.append(f"  精确率:     {metrics['precision']:.4f}")
            report_lines.append(f"  召回率:     {metrics['recall']:.4f}")
            report_lines.append(f"  特异性:     {metrics['specificity']:.4f}")
            report_lines.append(f"  平衡准确率: {metrics['balanced_accuracy']:.4f}")
            
            if 'auc' in metrics:
                report_lines.append(f"  AUC:        {metrics['auc']:.4f}")
            
            report_lines.append("")
            
            # 混淆矩阵
            report_lines.append("混淆矩阵:")
            cm = metrics['confusion_matrix']
            report_lines.append(f"  TN: {metrics['true_negatives']:4d}  |  FP: {metrics['false_positives']:4d}")
            report_lines.append(f"  FN: {metrics['false_negatives']:4d}  |  TP: {metrics['true_positives']:4d}")
            report_lines.append("")
        
        # 模型比较
        if len(model_results) > 1:
            report_lines.append("=== 模型性能排名 ===")
            df = self.compare_models(model_results, sort_by='f1')
            report_lines.append(df.to_string(index=False, float_format='%.4f'))
            report_lines.append("")
        
        report_lines.append("=" * 80)
        
        # 生成报告内容
        report_content = "\n".join(report_lines)
        
        # 保存报告
        if save_path:
            with open(save_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            print(f"评估报告已保存到: {save_path}")
        
        return report_content
