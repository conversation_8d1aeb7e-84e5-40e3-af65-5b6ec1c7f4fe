# SVM分类器实现说明

## 项目概述

本项目基于README.md中的技术规范，实现了一个完整的SVM（支持向量机）分类模型，用于游记流行度预测任务。

## 核心特性

### 1. 16维特征工程
- **已有的9个特征**: nima, hyperiqa, contrast, selfsimilarity, simplicity, symmetry, score_abs, score_con, score_coh
- **计算的7个特征**: related_travel_num, related_user_num, related_travel_p, related_user_p, follower, following, count

### 2. 时序数据处理
- 按uploadTime字段进行时间顺序排序
- 训练集:测试集 = 0.8:0.2 的时序划分
- k折时序交叉验证（默认5折）

### 3. SVM模型特性
- 自动特征标准化（均值0，标准差1）
- 超参数网格搜索调优
- 支持多种核函数（RBF、线性、多项式）
- 完整的模型评估指标

## 项目结构

```
baseline/
├── __init__.py                 # 模块初始化
├── data_preprocessing.py       # 数据预处理模块
├── feature_engineering.py     # 特征工程模块
├── time_series_cv.py          # 时序交叉验证模块
├── svm_classify.py            # SVM分类器模块
├── model_evaluation.py        # 模型评估模块
├── readme.md                  # 本说明文档
└── models/                    # 模型保存目录
    ├── __init__.py
    ├── svm_model.pkl          # 训练好的SVM模型
    └── evaluation_report.txt  # 评估报告

data/
├── __init__.py
└── data_0825.csv             # 数据文件（如不存在会自动创建示例数据）

main.py                       # 主程序入口
```

## 使用方法

### 1. 快速开始

```bash
# 运行完整的训练和评估流程
python main.py
```

### 2. 分步骤使用

```python
from baseline.data_preprocessing import DataPreprocessor
from baseline.svm_classify import SVMClassifier

# 1. 数据预处理
preprocessor = DataPreprocessor('data/data_0825.csv')
data = preprocessor.load_data()
data = preprocessor.compute_all_features(data)
train_data, test_data = preprocessor.split_by_time(data)

# 2. 训练SVM模型
svm_classifier = SVMClassifier()
svm_classifier.tune_hyperparameters(train_data, cv_folds=5)
svm_classifier.train(train_data)

# 3. 评估和预测
test_metrics = svm_classifier.evaluate(test_data)
y_pred, y_pred_proba = svm_classifier.predict(test_data)
```

### 3. 自定义超参数

```python
# 设置自定义超参数搜索空间
param_grid = {
    'C': [0.1, 1, 10, 100],
    'kernel': ['rbf', 'linear', 'poly'],
    'gamma': ['scale', 'auto', 0.001, 0.01, 0.1, 1]
}
svm_classifier.set_param_grid(param_grid)
```

## 特征计算说明

### 景点相关特征
- **related_travel_num**: 每个景点出现的不同travelId数量
- **related_user_num**: 每个景点涉及的不同userId数量  
- **related_travel_p**: related_travel_num / 总travelId数量
- **related_user_p**: related_user_num / 总userId数量

### 用户相关特征
- **count**: 每个userId对应的不同travelId数量
- **follower**: 用户粉丝数
- **following**: 用户关注数

### 特征处理策略
1. **训练阶段**: 基于训练集计算所有统计特征
2. **验证阶段**: 使用训练集的统计结果
3. **测试阶段**: 基于训练集+验证集重新统计
4. **未知数据**: 使用已有数据的平均值填充

## 模型评估指标

- **准确率 (Accuracy)**: 正确预测的样本比例
- **F1分数 (F1-Score)**: 精确率和召回率的调和平均
- **精确率 (Precision)**: 预测为正例中实际为正例的比例
- **召回率 (Recall)**: 实际正例中被正确预测的比例
- **AUC**: ROC曲线下面积
- **混淆矩阵**: 详细的分类结果统计

## 时序交叉验证

实现了专门的时序交叉验证器，确保：
1. 按时间顺序划分数据
2. 训练集始终在验证集之前
3. 避免使用未来数据预测过去
4. 每一折都重新计算特征统计信息

## 输出文件

### 1. 训练好的模型
- 路径: `baseline/models/svm_model.pkl`
- 包含: 完整的SVM模型、特征工程器、最佳超参数

### 2. 评估报告
- 路径: `baseline/models/evaluation_report.txt`
- 内容: 详细的性能指标、混淆矩阵、模型比较

## 依赖库

```
pandas>=1.3.0
numpy>=1.21.0
scikit-learn>=1.0.0
matplotlib>=3.3.0
seaborn>=0.11.0
joblib>=1.0.0
```

## 注意事项

1. **数据格式**: 确保数据包含所需的列名和格式
2. **时间字段**: uploadTime字段用于时序排序，格式应为可解析的日期时间
3. **景点字段**: spots字段使用"|"分隔多个景点
4. **内存使用**: 大数据集可能需要调整批处理大小
5. **计算时间**: 超参数调优可能需要较长时间，可以减少搜索空间

## 扩展功能

### 1. 添加新特征
在`FeatureEngineer`类中添加新的特征计算方法

### 2. 自定义评估指标
在`ModelEvaluator`类中添加新的评估函数

### 3. 模型集成
可以轻松扩展为多模型集成框架

## 故障排除

### 常见问题
1. **数据文件不存在**: 程序会自动创建示例数据
2. **内存不足**: 减少数据量或调整批处理大小
3. **特征计算错误**: 检查数据格式和缺失值处理
4. **模型训练失败**: 检查超参数设置和数据质量

### 调试建议
1. 使用小数据集进行测试
2. 检查数据预处理的中间结果
3. 验证特征计算的正确性
4. 监控内存和CPU使用情况

## 性能优化建议

1. **特征选择**: 使用特征重要性分析去除冗余特征
2. **数据采样**: 对于大数据集可以使用分层采样
3. **并行计算**: 利用多核CPU加速超参数搜索
4. **缓存机制**: 缓存特征计算结果避免重复计算
