"""
SVM分类器模块
实现SVM模型的训练、预测和超参数调优功能
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from sklearn.svm import SVC
from sklearn.model_selection import GridSearchCV
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score, roc_auc_score
from sklearn.metrics import classification_report, confusion_matrix
import joblib
import warnings
warnings.filterwarnings('ignore')

from .data_preprocessing import DataPreprocessor
from .feature_engineering import FeatureEngineer
from .time_series_cv import TimeSeriesValidator


class SVMClassifier:
    """
    SVM分类器类
    
    主要功能：
    1. SVM模型的训练和预测
    2. 超参数调优
    3. 模型评估和结果分析
    4. 模型保存和加载
    """
    
    def __init__(self, random_state: int = 42):
        """
        初始化SVM分类器
        
        参数:
            random_state (int): 随机种子
        """
        self.random_state = random_state
        self.model = None
        self.best_params = None
        self.feature_engineer = FeatureEngineer()
        self.data_preprocessor = None
        self.is_fitted = False
        
        # 默认超参数搜索空间
        self.param_grid = {
            'C': [0.1, 1, 10, 100],
            'kernel': ['rbf', 'linear', 'poly'],
            'gamma': ['scale', 'auto', 0.001, 0.01, 0.1, 1]
        }
    
    def set_param_grid(self, param_grid: Dict[str, List]) -> None:
        """
        设置超参数搜索空间
        
        参数:
            param_grid (Dict[str, List]): 超参数网格
        """
        self.param_grid = param_grid
        print(f"更新超参数搜索空间: {param_grid}")
    
    def tune_hyperparameters(self, data: pd.DataFrame, cv_folds: int = 5, 
                           scoring: str = 'f1', n_jobs: int = -1) -> Dict[str, Any]:
        """
        使用时序交叉验证进行超参数调优
        
        参数:
            data (pd.DataFrame): 训练数据
            cv_folds (int): 交叉验证折数
            scoring (str): 评分指标
            n_jobs (int): 并行作业数
            
        返回:
            Dict[str, Any]: 最佳超参数和验证结果
        """
        print("开始SVM超参数调优...")
        print(f"搜索空间: {self.param_grid}")
        
        # 确保数据按时间排序
        if 'uploadTime' in data.columns:
            data = data.sort_values('uploadTime').reset_index(drop=True)
        
        # 计算特征统计信息（基于全部训练数据）
        self.feature_engineer.compute_spot_statistics(data)
        self.feature_engineer.compute_user_statistics(data)
        
        # 提取特征
        data_with_features = self.feature_engineer.extract_all_features(data, use_cached_stats=False)
        X = data_with_features[self.feature_engineer.all_features].values
        y = data_with_features['label'].values
        
        # 特征缩放
        X_scaled = self.feature_engineer.fit_transform_features(X)
        
        print(f"特征矩阵形状: {X_scaled.shape}")
        print(f"正样本比例: {np.mean(y):.3f}")
        
        # 使用时序交叉验证进行网格搜索
        from .time_series_cv import TimeSeriesSplit
        tscv = TimeSeriesSplit(n_splits=cv_folds)
        
        # 创建SVM模型
        svm = SVC(random_state=self.random_state, probability=True)
        
        # 网格搜索
        grid_search = GridSearchCV(
            estimator=svm,
            param_grid=self.param_grid,
            cv=tscv,
            scoring=scoring,
            n_jobs=n_jobs,
            verbose=1,
            return_train_score=True
        )
        
        # 执行搜索
        grid_search.fit(X_scaled, y)
        
        # 保存最佳参数
        self.best_params = grid_search.best_params_
        self.model = grid_search.best_estimator_
        
        print(f"\n=== 超参数调优完成 ===")
        print(f"最佳参数: {self.best_params}")
        print(f"最佳{scoring}分数: {grid_search.best_score_:.4f}")
        
        # 返回详细结果
        results = {
            'best_params': self.best_params,
            'best_score': grid_search.best_score_,
            'cv_results': grid_search.cv_results_,
            'best_estimator': grid_search.best_estimator_
        }
        
        return results
    
    def train(self, data: pd.DataFrame, use_best_params: bool = True) -> 'SVMClassifier':
        """
        训练SVM模型
        
        参数:
            data (pd.DataFrame): 训练数据
            use_best_params (bool): 是否使用最佳超参数
            
        返回:
            SVMClassifier: 自身实例
        """
        print("开始训练SVM模型...")
        
        # 确保数据按时间排序
        if 'uploadTime' in data.columns:
            data = data.sort_values('uploadTime').reset_index(drop=True)
        
        # 计算特征统计信息
        self.feature_engineer.compute_spot_statistics(data)
        self.feature_engineer.compute_user_statistics(data)
        
        # 提取特征
        data_with_features = self.feature_engineer.extract_all_features(data, use_cached_stats=False)
        X = data_with_features[self.feature_engineer.all_features].values
        y = data_with_features['label'].values
        
        # 特征缩放
        X_scaled = self.feature_engineer.fit_transform_features(X)
        
        # 创建或更新模型
        if use_best_params and self.best_params is not None:
            print(f"使用最佳超参数: {self.best_params}")
            self.model = SVC(random_state=self.random_state, probability=True, **self.best_params)
        elif self.model is None:
            print("使用默认参数")
            self.model = SVC(random_state=self.random_state, probability=True)
        
        # 训练模型
        self.model.fit(X_scaled, y)
        self.is_fitted = True
        
        print("SVM模型训练完成")
        print(f"支持向量数量: {self.model.n_support_}")
        print(f"特征重要性可通过模型系数获取")
        
        return self
    
    def predict(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        使用训练好的模型进行预测
        
        参数:
            data (pd.DataFrame): 测试数据
            
        返回:
            Tuple[np.ndarray, np.ndarray]: (预测标签, 预测概率)
        """
        if not self.is_fitted:
            raise ValueError("模型未训练，请先调用train方法")
        
        print("开始预测...")
        
        # 提取特征（使用训练时的统计信息）
        data_with_features = self.feature_engineer.extract_all_features(data, use_cached_stats=True)
        X = data_with_features[self.feature_engineer.all_features].values
        
        # 特征缩放
        X_scaled = self.feature_engineer.transform_features(X)
        
        # 预测
        y_pred = self.model.predict(X_scaled)
        y_pred_proba = self.model.predict_proba(X_scaled)[:, 1]
        
        print(f"预测完成，预测样本数: {len(y_pred)}")
        print(f"预测正样本比例: {np.mean(y_pred):.3f}")
        
        return y_pred, y_pred_proba
    
    def evaluate(self, data: pd.DataFrame, return_detailed: bool = False) -> Dict[str, float]:
        """
        评估模型性能
        
        参数:
            data (pd.DataFrame): 测试数据（包含真实标签）
            return_detailed (bool): 是否返回详细评估结果
            
        返回:
            Dict[str, float]: 评估指标
        """
        if not self.is_fitted:
            raise ValueError("模型未训练，请先调用train方法")
        
        print("开始模型评估...")
        
        # 获取真实标签
        y_true = data['label'].values
        
        # 预测
        y_pred, y_pred_proba = self.predict(data)
        
        # 计算评估指标
        metrics = {
            'accuracy': accuracy_score(y_true, y_pred),
            'f1': f1_score(y_true, y_pred, average='binary'),
            'precision': precision_score(y_true, y_pred, average='binary', zero_division=0),
            'recall': recall_score(y_true, y_pred, average='binary', zero_division=0)
        }
        
        # 计算AUC
        try:
            metrics['auc'] = roc_auc_score(y_true, y_pred_proba)
        except ValueError:
            metrics['auc'] = 0.5
        
        # 打印结果
        print(f"\n=== 模型评估结果 ===")
        print(f"准确率 (Accuracy): {metrics['accuracy']:.4f}")
        print(f"F1分数 (F1-Score): {metrics['f1']:.4f}")
        print(f"精确率 (Precision): {metrics['precision']:.4f}")
        print(f"召回率 (Recall): {metrics['recall']:.4f}")
        print(f"AUC: {metrics['auc']:.4f}")
        
        if return_detailed:
            # 添加详细信息
            metrics['classification_report'] = classification_report(y_true, y_pred)
            metrics['confusion_matrix'] = confusion_matrix(y_true, y_pred)
            
            print(f"\n=== 分类报告 ===")
            print(metrics['classification_report'])
            print(f"\n=== 混淆矩阵 ===")
            print(metrics['confusion_matrix'])
        
        return metrics

    def save_model(self, filepath: str) -> None:
        """
        保存训练好的模型

        参数:
            filepath (str): 保存路径
        """
        if not self.is_fitted:
            raise ValueError("模型未训练，无法保存")

        model_data = {
            'model': self.model,
            'feature_engineer': self.feature_engineer,
            'best_params': self.best_params,
            'is_fitted': self.is_fitted
        }

        joblib.dump(model_data, filepath)
        print(f"模型已保存到: {filepath}")

    def load_model(self, filepath: str) -> 'SVMClassifier':
        """
        加载训练好的模型

        参数:
            filepath (str): 模型文件路径

        返回:
            SVMClassifier: 自身实例
        """
        model_data = joblib.load(filepath)

        self.model = model_data['model']
        self.feature_engineer = model_data['feature_engineer']
        self.best_params = model_data['best_params']
        self.is_fitted = model_data['is_fitted']

        print(f"模型已从 {filepath} 加载")
        return self

    def get_feature_importance(self) -> Optional[Dict[str, float]]:
        """
        获取特征重要性（对于线性核SVM）

        返回:
            Optional[Dict[str, float]]: 特征重要性字典
        """
        if not self.is_fitted:
            print("模型未训练，无法获取特征重要性")
            return None

        if self.model.kernel != 'linear':
            print(f"当前核函数为 {self.model.kernel}，无法直接获取特征重要性")
            return None

        # 获取特征系数
        coef = self.model.coef_[0]
        feature_names = self.feature_engineer.get_feature_importance_names()

        # 计算特征重要性（系数的绝对值）
        importance = {name: abs(coef[i]) for i, name in enumerate(feature_names)}

        # 按重要性排序
        sorted_importance = dict(sorted(importance.items(), key=lambda x: x[1], reverse=True))

        print("\n=== 特征重要性 (线性SVM系数绝对值) ===")
        for feature, imp in sorted_importance.items():
            print(f"{feature:20s}: {imp:.6f}")

        return sorted_importance

    def cross_validate(self, data: pd.DataFrame, cv_folds: int = 5) -> Dict[str, Any]:
        """
        执行时序交叉验证

        参数:
            data (pd.DataFrame): 训练数据
            cv_folds (int): 交叉验证折数

        返回:
            Dict[str, Any]: 交叉验证结果
        """
        print(f"开始{cv_folds}折时序交叉验证...")

        # 使用最佳参数或默认参数创建模型
        if self.best_params is not None:
            model = SVC(random_state=self.random_state, probability=True, **self.best_params)
        else:
            model = SVC(random_state=self.random_state, probability=True)

        # 创建验证器
        validator = TimeSeriesValidator(n_splits=cv_folds)

        # 执行验证
        fold_results = validator.validate_model(model, data, self.feature_engineer)

        # 计算平均结果
        avg_results = validator._compute_average_results()

        return {
            'fold_results': fold_results,
            'average_results': avg_results,
            'best_fold': validator.get_best_fold()
        }

    def print_model_info(self) -> None:
        """
        打印模型信息
        """
        print("\n=== SVM模型信息 ===")
        print(f"模型状态: {'已训练' if self.is_fitted else '未训练'}")

        if self.model is not None:
            print(f"核函数: {self.model.kernel}")
            if hasattr(self.model, 'C'):
                print(f"正则化参数C: {self.model.C}")
            if hasattr(self.model, 'gamma'):
                print(f"核函数参数gamma: {self.model.gamma}")
            if self.is_fitted:
                print(f"支持向量数量: {self.model.n_support_}")

        if self.best_params is not None:
            print(f"最佳超参数: {self.best_params}")

        print(f"特征数量: {len(self.feature_engineer.all_features)}")
        print(f"特征列表: {self.feature_engineer.all_features}")

        # 打印特征统计信息
        self.feature_engineer.print_feature_stats()


def main():
    """
    主函数：演示SVM分类器的使用
    """
    print("=== SVM分类器演示 ===")

    # 创建数据预处理器
    preprocessor = DataPreprocessor()

    # 处理数据
    X_train, X_test, y_train, y_test = preprocessor.process_data()

    # 重新构建DataFrame用于SVM分类器
    train_data = preprocessor.data.iloc[:len(X_train)].copy()
    test_data = preprocessor.data.iloc[len(X_train):].copy()

    # 创建SVM分类器
    svm_classifier = SVMClassifier()

    # 超参数调优
    tuning_results = svm_classifier.tune_hyperparameters(train_data, cv_folds=3)

    # 训练模型
    svm_classifier.train(train_data)

    # 评估模型
    test_metrics = svm_classifier.evaluate(test_data, return_detailed=True)

    # 交叉验证
    cv_results = svm_classifier.cross_validate(train_data, cv_folds=3)

    # 打印模型信息
    svm_classifier.print_model_info()

    # 获取特征重要性
    svm_classifier.get_feature_importance()

    # 保存模型
    svm_classifier.save_model('baseline/models/svm_model.pkl')

    print("\n=== SVM分类器演示完成 ===")


if __name__ == "__main__":
    main()
