"""
时序交叉验证模块
实现k折时序交叉验证，确保按时间顺序划分数据集
"""

import pandas as pd
import numpy as np
from typing import List, Tuple, Iterator, Optional, Dict
from sklearn.model_selection import BaseCrossValidator
import warnings
warnings.filterwarnings('ignore')


class TimeSeriesSplit(BaseCrossValidator):
    """
    时序交叉验证器
    
    按时间顺序将数据分成k折，每一折都使用之前的数据作为训练集，
    当前折作为验证集，确保不会使用未来的数据来预测过去
    """
    
    def __init__(self, n_splits: int = 5, test_size: Optional[int] = None):
        """
        初始化时序交叉验证器
        
        参数:
            n_splits (int): 交叉验证的折数，默认5
            test_size (Optional[int]): 每个验证集的大小，如果为None则自动计算
        """
        self.n_splits = n_splits
        self.test_size = test_size
    
    def get_n_splits(self, X=None, y=None, groups=None) -> int:
        """
        获取交叉验证的折数
        
        返回:
            int: 折数
        """
        return self.n_splits
    
    def split(self, X, y=None, groups=None) -> Iterator[Tuple[np.ndarray, np.ndarray]]:
        """
        生成训练集和验证集的索引
        
        参数:
            X: 特征矩阵或数据长度
            y: 标签向量（可选）
            groups: 分组信息（可选）
            
        返回:
            Iterator[Tuple[np.ndarray, np.ndarray]]: (训练集索引, 验证集索引)的迭代器
        """
        n_samples = len(X) if hasattr(X, '__len__') else X
        
        if self.test_size is None:
            # 自动计算验证集大小
            test_size = n_samples // (self.n_splits + 1)
        else:
            test_size = self.test_size
        
        # 确保有足够的数据进行交叉验证
        min_train_size = test_size
        if n_samples < min_train_size + test_size * self.n_splits:
            raise ValueError(f"数据量不足进行{self.n_splits}折交叉验证")
        
        # 生成每一折的索引
        for i in range(self.n_splits):
            # 训练集：从开始到当前验证集之前的所有数据
            train_end = min_train_size + i * test_size
            train_indices = np.arange(0, train_end)
            
            # 验证集：当前折的数据
            val_start = train_end
            val_end = min(val_start + test_size, n_samples)
            val_indices = np.arange(val_start, val_end)
            
            if len(val_indices) == 0:
                break
                
            yield train_indices, val_indices


class TimeSeriesValidator:
    """
    时序验证器类
    
    主要功能：
    1. 管理时序交叉验证流程
    2. 处理特征计算和缩放
    3. 记录验证结果
    """
    
    def __init__(self, n_splits: int = 5):
        """
        初始化时序验证器
        
        参数:
            n_splits (int): 交叉验证折数
        """
        self.n_splits = n_splits
        self.cv = TimeSeriesSplit(n_splits=n_splits)
        self.fold_results = []
    
    def validate_model(self, model, data: pd.DataFrame, feature_engineer, 
                      target_col: str = 'label') -> List[Dict]:
        """
        执行时序交叉验证
        
        参数:
            model: 机器学习模型（需要有fit和predict方法）
            data (pd.DataFrame): 按时间排序的数据
            feature_engineer: 特征工程器
            target_col (str): 目标列名
            
        返回:
            List[Dict]: 每一折的验证结果
        """
        print(f"开始{self.n_splits}折时序交叉验证...")
        
        # 确保数据按时间排序
        if 'uploadTime' in data.columns:
            data = data.sort_values('uploadTime').reset_index(drop=True)
        
        self.fold_results = []
        
        # 执行交叉验证
        for fold, (train_idx, val_idx) in enumerate(self.cv.split(data)):
            print(f"\n=== 第 {fold + 1} 折验证 ===")
            print(f"训练集大小: {len(train_idx)}, 验证集大小: {len(val_idx)}")
            
            # 分割数据
            train_data = data.iloc[train_idx].copy()
            val_data = data.iloc[val_idx].copy()
            
            # 在训练集上计算特征统计信息
            feature_engineer.compute_spot_statistics(train_data)
            feature_engineer.compute_user_statistics(train_data)
            
            # 提取训练集特征
            train_data = feature_engineer.extract_all_features(train_data, use_cached_stats=False)
            X_train = train_data[feature_engineer.all_features].values
            y_train = train_data[target_col].values
            
            # 提取验证集特征（使用训练集的统计信息）
            val_data = feature_engineer.extract_all_features(val_data, use_cached_stats=True)
            X_val = val_data[feature_engineer.all_features].values
            y_val = val_data[target_col].values
            
            # 特征缩放
            X_train_scaled = feature_engineer.fit_transform_features(X_train)
            X_val_scaled = feature_engineer.transform_features(X_val)
            
            # 训练模型
            model.fit(X_train_scaled, y_train)
            
            # 预测
            y_pred = model.predict(X_val_scaled)
            y_pred_proba = None
            if hasattr(model, 'predict_proba'):
                y_pred_proba = model.predict_proba(X_val_scaled)[:, 1]
            elif hasattr(model, 'decision_function'):
                y_pred_proba = model.decision_function(X_val_scaled)
            
            # 计算评估指标
            fold_result = self._compute_metrics(y_val, y_pred, y_pred_proba)
            fold_result['fold'] = fold + 1
            fold_result['train_size'] = len(train_idx)
            fold_result['val_size'] = len(val_idx)
            
            self.fold_results.append(fold_result)
            
            # 打印当前折结果
            print(f"准确率: {fold_result['accuracy']:.4f}")
            print(f"F1分数: {fold_result['f1']:.4f}")
            print(f"精确率: {fold_result['precision']:.4f}")
            print(f"召回率: {fold_result['recall']:.4f}")
        
        # 计算平均结果
        avg_results = self._compute_average_results()
        print(f"\n=== 交叉验证平均结果 ===")
        print(f"平均准确率: {avg_results['accuracy']:.4f} ± {avg_results['accuracy_std']:.4f}")
        print(f"平均F1分数: {avg_results['f1']:.4f} ± {avg_results['f1_std']:.4f}")
        print(f"平均精确率: {avg_results['precision']:.4f} ± {avg_results['precision_std']:.4f}")
        print(f"平均召回率: {avg_results['recall']:.4f} ± {avg_results['recall_std']:.4f}")
        
        return self.fold_results
    
    def _compute_metrics(self, y_true: np.ndarray, y_pred: np.ndarray, 
                        y_pred_proba: Optional[np.ndarray] = None) -> Dict:
        """
        计算评估指标
        
        参数:
            y_true (np.ndarray): 真实标签
            y_pred (np.ndarray): 预测标签
            y_pred_proba (Optional[np.ndarray]): 预测概率
            
        返回:
            Dict: 评估指标字典
        """
        from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score, roc_auc_score
        
        metrics = {
            'accuracy': accuracy_score(y_true, y_pred),
            'f1': f1_score(y_true, y_pred, average='binary'),
            'precision': precision_score(y_true, y_pred, average='binary', zero_division=0),
            'recall': recall_score(y_true, y_pred, average='binary', zero_division=0)
        }
        
        # 如果有预测概率，计算AUC
        if y_pred_proba is not None:
            try:
                metrics['auc'] = roc_auc_score(y_true, y_pred_proba)
            except ValueError:
                metrics['auc'] = 0.5  # 如果只有一个类别，AUC设为0.5
        
        return metrics
    
    def _compute_average_results(self) -> Dict:
        """
        计算平均验证结果
        
        返回:
            Dict: 平均结果和标准差
        """
        if not self.fold_results:
            return {}
        
        metrics = ['accuracy', 'f1', 'precision', 'recall']
        avg_results = {}
        
        for metric in metrics:
            values = [result[metric] for result in self.fold_results]
            avg_results[metric] = np.mean(values)
            avg_results[f'{metric}_std'] = np.std(values)
        
        # 如果有AUC，也计算平均值
        if 'auc' in self.fold_results[0]:
            auc_values = [result['auc'] for result in self.fold_results]
            avg_results['auc'] = np.mean(auc_values)
            avg_results['auc_std'] = np.std(auc_values)
        
        return avg_results
    
    def get_best_fold(self, metric: str = 'f1') -> Dict:
        """
        获取指定指标下的最佳折
        
        参数:
            metric (str): 评估指标名称
            
        返回:
            Dict: 最佳折的结果
        """
        if not self.fold_results:
            return {}
        
        best_fold = max(self.fold_results, key=lambda x: x.get(metric, 0))
        return best_fold
