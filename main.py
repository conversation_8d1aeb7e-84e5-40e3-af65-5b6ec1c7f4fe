"""
SVM分类器主程序
完整的训练和预测流程演示
"""

import os
import sys
import pandas as pd
import numpy as np
from typing import Dict, Any
import warnings
warnings.filterwarnings('ignore')

# 添加baseline模块到路径
sys.path.append('baseline')

from baseline.data_preprocessing import DataPreprocessor
from baseline.feature_engineering import FeatureEngineer
from baseline.svm_classify import SVMClassifier
from baseline.model_evaluation import ModelEvaluator
from baseline.time_series_cv import TimeSeriesValidator


def create_sample_data(save_path: str = 'data/data_0825.csv') -> None:
    """
    创建示例数据文件
    
    参数:
        save_path (str): 数据保存路径
    """
    print("创建示例数据文件...")
    
    # 确保目录存在
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    
    # 生成示例数据
    np.random.seed(42)
    n_samples = 2000
    
    # 创建示例数据
    data = {
        'userId': np.random.randint(100000, 999999, n_samples),
        'travelId': np.random.randint(1000000, 9999999, n_samples),
        'uploadTime': pd.date_range('2020-01-01', '2025-08-01', periods=n_samples),
        'follower': np.random.randint(0, 10000, n_samples),
        'following': np.random.randint(0, 5000, n_samples),
        'spots': [f"景点{i%20}|景点{(i+1)%20}|景点{(i+2)%20}" for i in range(n_samples)],
        'label': np.random.randint(0, 2, n_samples),
        # 已有的9个特征
        'nima': np.random.normal(5, 1, n_samples),
        'hyperiqa': np.random.normal(0.5, 0.2, n_samples),
        'contrast': np.random.normal(0, 0.5, n_samples),
        'selfsimilarity': np.random.normal(0, 0.3, n_samples),
        'simplicity': np.random.normal(0.5, 0.2, n_samples),
        'symmetry': np.random.normal(0.5, 0.2, n_samples),
        'score_abs': np.random.normal(4, 1, n_samples),
        'score_con': np.random.normal(4, 1, n_samples),
        'score_coh': np.random.normal(4, 1, n_samples),
    }
    
    df = pd.DataFrame(data)
    df.to_csv(save_path, index=False)
    print(f"示例数据已保存到: {save_path}")
    print(f"数据形状: {df.shape}")
    print(f"正样本比例: {df['label'].mean():.3f}")


def train_svm_model(data_path: str = 'data/data_0825.csv') -> Dict[str, Any]:
    """
    训练SVM模型的完整流程
    
    参数:
        data_path (str): 数据文件路径
        
    返回:
        Dict[str, Any]: 训练结果
    """
    print("\n" + "="*60)
    print("开始SVM模型训练流程")
    print("="*60)
    
    # 1. 数据预处理
    print("\n=== 步骤1: 数据预处理 ===")
    preprocessor = DataPreprocessor(data_path)
    data = preprocessor.load_data()
    
    # 计算特征
    data = preprocessor.compute_all_features(data)  # TODO 这里对所有数据计算所有特征，应该在划分数据后，仅基于训练数据来计算
    
    # 数据清洗
    data = preprocessor.clean_data(data)
    
    # 按时间划分数据集
    train_data, test_data = preprocessor.split_by_time(data, test_ratio=0.2)
    
    print(f"训练集大小: {len(train_data)}")
    print(f"测试集大小: {len(test_data)}")
    
    # 2. 创建SVM分类器
    print("\n=== 步骤2: 创建SVM分类器 ===")
    svm_classifier = SVMClassifier(random_state=42)
    
    # 设置超参数搜索空间（简化版，用于快速演示）
    param_grid = {
        'C': [0.1, 1, 10],
        'kernel': ['rbf', 'linear'],
        'gamma': ['scale', 'auto']
    }
    svm_classifier.set_param_grid(param_grid)
    
    # 3. 超参数调优
    print("\n=== 步骤3: 超参数调优 ===")
    tuning_results = svm_classifier.tune_hyperparameters(
        train_data, 
        cv_folds=3,  # 使用3折交叉验证以加快速度
        scoring='f1'
    )
    
    # 4. 训练最终模型
    print("\n=== 步骤4: 训练最终模型 ===")
    svm_classifier.train(train_data, use_best_params=True)
    
    # 5. 模型评估
    print("\n=== 步骤5: 模型评估 ===")
    
    # 在测试集上评估
    test_metrics = svm_classifier.evaluate(test_data, return_detailed=True)
    
    # 交叉验证评估
    cv_results = svm_classifier.cross_validate(train_data, cv_folds=3)
    
    # 6. 打印模型信息
    print("\n=== 步骤6: 模型信息 ===")
    svm_classifier.print_model_info()
    
    # 获取特征重要性（如果是线性核）
    feature_importance = svm_classifier.get_feature_importance()
    
    # 7. 保存模型
    print("\n=== 步骤7: 保存模型 ===")
    os.makedirs('baseline/models', exist_ok=True)
    model_path = 'baseline/models/svm_model.pkl'
    svm_classifier.save_model(model_path)
    
    # 返回结果
    results = {
        'svm_classifier': svm_classifier,
        'train_data': train_data,
        'test_data': test_data,
        'tuning_results': tuning_results,
        'test_metrics': test_metrics,
        'cv_results': cv_results,
        'feature_importance': feature_importance,
        'model_path': model_path
    }
    
    print("\n" + "="*60)
    print("SVM模型训练流程完成")
    print("="*60)
    
    return results


def evaluate_model_performance(results: Dict[str, Any]) -> None:
    """
    详细评估模型性能
    
    参数:
        results (Dict[str, Any]): 训练结果
    """
    print("\n" + "="*60)
    print("详细模型性能评估")
    print("="*60)
    
    svm_classifier = results['svm_classifier']
    test_data = results['test_data']
    
    # 创建评估器
    evaluator = ModelEvaluator()
    
    # 获取预测结果
    y_true = test_data['label'].values
    y_pred, y_pred_proba = svm_classifier.predict(test_data)
    
    # 评估模型
    evaluation_result = evaluator.evaluate_model(
        model_name="SVM分类器",
        y_true=y_true,
        y_pred=y_pred,
        y_pred_proba=y_pred_proba,
        additional_info={
            'best_params': results['tuning_results']['best_params'],
            'cv_score': results['tuning_results']['best_score']
        }
    )
    
    # 生成评估报告
    report_path = 'baseline/models/evaluation_report.txt'
    report_content = evaluator.generate_evaluation_report(
        [evaluation_result], 
        save_path=report_path
    )
    
    print(f"\n详细评估报告已保存到: {report_path}")


def demonstrate_prediction(results: Dict[str, Any]) -> None:
    """
    演示预测功能
    
    参数:
        results (Dict[str, Any]): 训练结果
    """
    print("\n" + "="*60)
    print("预测功能演示")
    print("="*60)
    
    svm_classifier = results['svm_classifier']
    test_data = results['test_data']
    
    # 选择几个样本进行预测演示
    sample_data = test_data.head(10).copy()
    
    print("=== 预测样本信息 ===")
    print(f"样本数量: {len(sample_data)}")
    print(f"真实标签分布: {sample_data['label'].value_counts().to_dict()}")
    
    # 进行预测
    y_pred, y_pred_proba = svm_classifier.predict(sample_data)
    
    # 显示预测结果
    print("\n=== 预测结果 ===")
    results_df = pd.DataFrame({
        'travelId': sample_data['travelId'].values,
        '真实标签': sample_data['label'].values,
        '预测标签': y_pred,
        '预测概率': y_pred_proba,
        '预测正确': sample_data['label'].values == y_pred
    })
    
    print(results_df.to_string(index=False, float_format='%.4f'))
    
    accuracy = np.mean(results_df['预测正确'])
    print(f"\n样本预测准确率: {accuracy:.4f}")


def main():
    """
    主函数：完整的SVM分类器演示流程
    """
    print("SVM分类器完整演示程序")
    print("基于README.md要求实现的16维特征SVM分类模型")
    
    # 检查数据文件是否存在，如果不存在则创建示例数据
    data_path = 'data/data_0825.csv'
    if not os.path.exists(data_path):
        print(f"\n数据文件 {data_path} 不存在，创建示例数据...")
        create_sample_data(data_path)
    
    try:
        # 1. 训练SVM模型
        training_results = train_svm_model(data_path)
        
        # 2. 详细性能评估
        evaluate_model_performance(training_results)
        
        # 3. 预测功能演示
        demonstrate_prediction(training_results)
        
        # 4. 总结
        print("\n" + "="*60)
        print("程序执行完成")
        print("="*60)
        print("主要输出文件:")
        print(f"- 训练好的模型: {training_results['model_path']}")
        print("- 评估报告: baseline/models/evaluation_report.txt")
        print("\n模型特点:")
        print("- 使用16维特征（9个已有特征 + 7个计算特征）")
        print("- 支持时序交叉验证")
        print("- 自动超参数调优")
        print("- 特征标准化处理")
        print("- 完整的评估指标计算")
        
        # 打印最终性能
        test_metrics = training_results['test_metrics']
        print(f"\n=== 最终测试集性能 ===")
        print(f"准确率: {test_metrics['accuracy']:.4f}")
        print(f"F1分数: {test_metrics['f1']:.4f}")
        print(f"精确率: {test_metrics['precision']:.4f}")
        print(f"召回率: {test_metrics['recall']:.4f}")
        if 'auc' in test_metrics:
            print(f"AUC: {test_metrics['auc']:.4f}")
        
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
